Feature: Review

  @e2e @review
  Scenario: The user opens the review dialog
    When The user opens the review dialog

  @e2e @review
  Scenario: The user approves a report
    When The user approves a report

  @e2e @review
  Scenario: The user approves a report by clicking the approve icon
    When The user approves a report by clicking the approve icon

  @e2e @review
  Scenario: The user denies a report
    When The user denies a report

  @e2e @review @skip
  Scenario: The user edits a report location
    When The user edits a report location
    # This scenario is currently skipped because the feature is not implemented in the web application.

  @e2e @review
  Scenario: The user edits a stop field description
    When The user edits a stop field description

  @e2e @review
  Scenario: The user edits a stop field description multiple times
    When The user edits a stop field description multiple times
