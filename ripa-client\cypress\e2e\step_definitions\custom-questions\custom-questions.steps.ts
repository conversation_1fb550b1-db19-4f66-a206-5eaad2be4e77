import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { getByTestId, type } from '../../../support/utility';
import { logInAsSuperAdmin, createFormThroughStep4, logInAsOfficer, createAndSubmitForm } from '../../../support/actions';
const step5ThroughSubmit = () => {
  // Single select pick option 2
  getByTestId('dynamic-select-form-box-1').click();
  getByTestId('dynamic-select-form-box-1').should('have.class', 'active');
 
  // Check helper text
  cy.get('.ripa-form-container__helper-box-text').should('have.text', 'Helping you');
 
  getByTestId('ripa-form-container-continue').click();
 
  // Multiple select pick both options
  getByTestId('dynamic-select-form-box-0').click();
  getByTestId('dynamic-select-form-box-1').click();
 
  getByTestId('ripa-form-container-continue').click();
 
  // Single select pick option 2 for 2nd person
  getByTestId('dynamic-select-form-box-1').click();
  getByTestId('dynamic-select-form-box-1').should('have.class', 'active');
 
  getByTestId('ripa-form-container-continue').click();
 
  // Multiple select pick both options for 2nd person
  getByTestId('dynamic-select-form-box-0').click();
  getByTestId('dynamic-select-form-box-1').click();
 
  getByTestId('ripa-form-container-continue').click();
 
  // Skip through not required select
  getByTestId('ripa-form-container-continue').click();
 
  // Check default value
  getByTestId('dynamic-numeric-form-input').should('have.value', '40');
 
  getByTestId('ripa-form-container-continue').click();
 
  // Should not advance
  type('dynamic-numeric-form-input', '2', { validate: false });
  getByTestId('ripa-form-container-continue').click();
  // clearSnackBars();
  cy.get('.snackbar__box', { timeout: 10000 }).should('not.exist');
 
  // Go next
  getByTestId('dynamic-numeric-form-input').clear().type('1');
  getByTestId('ripa-form-container-continue').click();
 
  getByTestId('ripa-form-container-continue').click();
 
  cy.url().should('include', '/dashboard');
};
 
Before(() => {
  logInAsSuperAdmin();
  getByTestId('header-navigation').should('exist');
  cy.get('div.auth-provider__loading').should('not.exist');

  getByTestId('header-navigation').click();

  getByTestId('header-veritone-nav-question-designer').click();

  cy.url().should('include', '/custom_questions');

  cy.get('.label-dropdown__dropdown').click();
  // Wait for menu to be visible instead of arbitrary wait
  cy.get('.MuiMenu-list').should('be.visible');
  cy.get('.MuiMenu-list').children().should('have.length.greaterThan', 1)
  .eq(2)
  .click();

  // Wait for menu to close
  cy.get('.MuiMenu-list').should('not.exist');
});
 
When('The user ensures non-live questions do not appear in form', () => {
  // Add to per-person section (Select form already selected)
  getByTestId('custom-questions-cc-add-question-0').click();
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-0-"]').first().click();
 
  // Change question title text
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Question 1').should('have.value', 'Question 1');
 
  getByTestId('custom-questions-set').click();
 
  logInAsOfficer();
 
  createAndSubmitForm(false);
});
 
When('The user adds questions, goes live, and views them in new report', () => {
  // Delete question from previous test - `force` is used due to lack of `hover` in cypress
  // getByTestId('custom-question-cc-list-section-question-delete-0-0').click({ force: true });
 
  // Question 1
  // Add select question to per-person section
  getByTestId('custom-questions-cc-add-question-0').click({force: true});
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-"]').first().click();
 
  // Change title
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Required single answer select').should('have.value', 'Required single answer select');
 
  // Add option
  cy.get('.custom-questions__cc-edit-array-add').click();
 
  // Change option names
  getByTestId('custom-questions__cc-array-options-0').clear().type('One').should('have.value', 'One');
  getByTestId('custom-questions__cc-array-options-1').clear().type('Two').should('have.value', 'Two');
  getByTestId('custom-questions__cc-array-options-2').clear().type('Three').should('have.value', 'Three');
 
  // Turn multiple off
  getByTestId('custom-questions__cc-edit-bool-multiple').click();
 
  // Type in helper text
  getByTestId('custom-questions__cc-edit-string-helperText').clear().type('Helping you').should('have.value', 'Helping you');
 
  // Question 2
  // Add select question to per-person section
  getByTestId('custom-questions-cc-add-question-0').click();
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-0-"]').eq(1).click();
 
  // Change title
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Required multiple answer select').should('have.value', 'Required multiple answer select');
 
  // Add option
  cy.get('.custom-questions__cc-edit-array-add').click();
 
  // Delete option
  cy.get('.custom-questions__cc-edit-array-option-container').last()
  .children().first()
  .click();
 
  // Set box width
  getByTestId('custom-questions__cc-edit-number-boxWidth').clear().type('{selectall}');
  type('custom-questions__cc-edit-number-boxWidth', '150');
 
  // Set box height
  getByTestId('custom-questions__cc-edit-number-boxHeight').clear().type('{selectall}');
  type('custom-questions__cc-edit-number-boxHeight', '75');
 
  // Set box padding
  getByTestId('custom-questions__cc-edit-number-boxPadding').clear().type('{selectall}');
  type('custom-questions__cc-edit-number-boxPadding', '10');
 
  // Question 3
  // Add select question to per-stop section
  getByTestId('custom-questions-cc-add-question-0').click();
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-0-"]').eq(2).click();
 
  // Change title
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Not required multiple answer select').should('have.value', 'Not required multiple answer select');
 
  // Turn off required
  getByTestId('custom-questions__cc-edit-bool-required').click();
 
  // Question 4
  // Add select qustion to per-person section
  getByTestId('custom-questions-cc-add-question-0').click();
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-0-"]').eq(3).click();
 
  // Change title
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Disabled select').should('have.value', 'Disabled select');
 
  // Disable Question
  getByTestId('custom-questions__cc-edit-bool-disabled').click();
 
  // Question 5
  // Set question type to numeric
  getByTestId('custom-questions-add-cc').click();
  cy.get('.MuiList-root').children().eq(1).click();
 
  // Add numeric question to per-stop section
  getByTestId('custom-questions-cc-add-question-1').click();
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-1-"]').last().click();
 
  // Change title
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Default value numeric').should('have.value', 'Default value numeric');
 
  // Set default value
  getByTestId('custom-questions__cc-edit-string-defaultValue').clear().type('40').should('have.value', '40');
 
  // Question 6
  // Add numeric question to per-stop section
  getByTestId('custom-questions-cc-add-question-1').click();
 
  // Click created question
  cy.get('*[data-testid^="custom-questions-question-1-"]').last().click();
 
  // Change title
  getByTestId('custom-questions__cc-edit-string-title').clear().type('Toggled numeric with min/max').should('have.value', 'Toggled numeric with min/max');
 
  // Change min and max
  getByTestId('custom-questions__cc-edit-number-max').clear().type('{selectall}');
  type('custom-questions__cc-edit-number-max', '1');
  getByTestId('custom-questions__cc-edit-number-min').clear().type('{selectall}');
  type('custom-questions__cc-edit-number-min', '0');
 
  // // Drag 3rd per-stop question into 2nd position
  // cy.get(`*[data-testid^="custom-questions-question-1-"]`).last()
  //   .trigger('mousedown', { which: 1 })
  //   .trigger('mousemove', { clientX: 0, clientY: -50 })
  //   .trigger('mouseup', { force: true });
 
  // Go live
  //Not able to trigger
  // getByTestId('custom-questions-cc-list-live-switch').click();
 
  // // Save questions
  // getByTestId('custom-questions-set').click();
 
  // // cy.wait(200);
 
  // getByTestId('custom-questions-cc-list-live-switch').should('have.class', 'is-on');
 
  logInAsOfficer();
 
  // Go through form up to custom questions
  createFormThroughStep4(false, '00:01');
 
  // Fill in custom questions and submit
  // step5ThroughSubmit();
});
 
When('The user goes offline and creates a form, submitting it for review', () => {
  Cypress.session.clearAllSavedSessions();
  cy.viewport(1000, 1000);

  logInAsOfficer();

  // cy.window().then(w => {
  //   // Application must be running in production mode for this test!
  //   // Use yarn run-prd-ssl
  //   expect(w.isDevelopment).to.equal(false)
  // })

  getByTestId('header-navigation').click();

  getByTestId('header-veritone-nav-signout').click();

  // Wait for logout to complete by checking URL change
  cy.url().should('include', '/log_in');

  cy.window().then(() => {
    cy.decoupledDispatch('Config.updateOnlineStatusWithLock', { isOnline: false, lockOnlineState: true });
  });

  // Fill in login credentials for offline login
  // Wait for page to stabilize and login form to be ready
  cy.get('div.auth-provider__loading').should('not.exist');
  getByTestId('login-username-field').should('be.visible').should('be.enabled');

  // Break up the chain to handle re-renders
  getByTestId('login-username-field').as('usernameField');
  getByTestId('login-pw-field').as('passwordField');
  getByTestId('login-submit').as('submitButton');

  // Type credentials with retry logic
  cy.get('@usernameField').clear().type('demo_officer_1').should('have.value', 'demo_officer_1');
  cy.get('@passwordField').clear().type('ripa_demo').should('have.value', 'ripa_demo');

  // Submit the form
  cy.get('@submitButton').click();

  // Wait for login to complete before proceeding
  cy.url().should('not.include', '/log_in');
  getByTestId('header-navigation').should('exist');
  getByTestId('loading').should('not.exist');

  cy.mockGeolocation({ latitude: 0, longitude: 0 });
  createFormThroughStep4(true, '00:02');

  step5ThroughSubmit();
});
 