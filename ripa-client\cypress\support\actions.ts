import dayjs from 'dayjs';
import { clearSnackBars, clickTestIds, getByTestId, type, typeLogin } from './utility';

export const logInAs = (username: string, password: string, landingPage: string | null = null, subdomain = 'demo'): void => {
  cy.session(
    [username, password, landingPage, subdomain],
    () => {
      cy.visit(`https://${subdomain || 'demo'}.contact-dev.com:2222/log_in`);
      // Wait for login form to be ready instead of arbitrary wait
      getByTestId('login-username-field').should('be.visible');
      typeLogin('login-username-field', username);
      typeLogin('login-pw-field', password);

      getByTestId('login-submit').click();
      cy.url().should('not.include', '/log_in');
      if (landingPage) {
        cy.url().should('include', landingPage);
      }
    },
    {
      validate() {
        cy.request('/api/v1/users/session').its('status').should('eq', 200);
      },
    }
  );
  cy.visit(`https://${subdomain}.contact-dev.com:2222${landingPage || '/'}`);
  getByTestId('header-navigation').should('exist');
  cy.get('div.auth-provider__loading').should('not.exist');
  getByTestId('loading').should('not.exist');
};

export const logInAsReviewer = (): void => {
  logInAs('demo_reviewer_1', 'ripa_demo', '/review');
};

export const logInAsOfficer = (): void => {
  logInAs('demo_officer_1', 'ripa_demo', '/dashboard');
};

export const logInAsAdmin = (): void => {
  logInAs('demo_admin_1', 'ripa_demo', '/admin/users');
};

export const logInAsSuperAdmin = (): void => {
  logInAs('veritone_cs', 'ripa_demo', '/admin/users', 'veri-admin');
};

export const updateUserSettings = (): void => {
  // Wait for dialog to be visible instead of arbitrary wait
  getByTestId('confirm-dialog-yes-button').should('be.visible');

  cy.get('body').then((bodyElement) => {
    if (bodyElement.find('[data-testid="confirm-dialog"]').length > 0) {
      getByTestId('race-of-officer-select').click();
      getByTestId('race-of-officer-select-checkbox-1').click();
      getByTestId('confirm-dialog-yes-button').click({ force: true });
    }
  });
};

export const createAndSubmitForm = (keepOffline: boolean): void => {
  updateUserSettings();
  getByTestId('dashboard-filters-new-report').not('.disabled').click();
  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
    // Wait for dialog to close
    getByTestId('confirm-dialog').should('not.exist');
  }
  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));
  getByTestId('ripa-form-container-continue').click();
  type('ripa-time-form-input', '00:00');
  clickTestIds(['ripa-form-container-continue', 'ripa-response-to-call-yes', 'ripa-form-container-continue', 'ripa-duration-form-20']);
  clearSnackBars();
  clickTestIds(['ripa-form-container-continue']);
  cy.get('[data-testid="ripa-action-taken-form-action-tab-school"]').click();
  cy.get('[data-testid="ripa-tabbed-location-form-school-autocomplete-input"]')
  .click();
  cy.get('*[data-option-index=2]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-type-of-assignment-officer-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-type-of-stop-select-dropdown"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-people-form-people-counter-number"]').contains('1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-gender-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-age-form-input-0"]').type('1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-race-form-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-disability-form-2024__sexual-orientation-select-0"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-primary-reason-form-box-0"]').click();

  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-traffic-violation-form-autocomplete-input"]').type('32 - ACCESSORY');
  cy.get('[data-testid="ripa-traffic-violation-form-box-0"]').click();
  getByTestId('ripa-form-container-continue').click({force: true});
  cy.get('[data-testid="ripa-reason-given-stopped-person-form-box-0"]').click();
  
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="pii-text-field"]').type('test 1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-action-taken-form-box-0-0"]')
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-action-taken-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-contraband-form-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-result-of-stop-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-result-of-stop-form-autocomplete-input-0-0"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-form-container-continue"]').click();
  cy.url().should('include', '/dashboard');
};

export const createFormThroughStep4 = (keepOffline: boolean, time: string): void => {
  getByTestId('dashboard-filters-new-report').not('.disabled').click();

  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
    // Wait for dialog to close
    getByTestId('confirm-dialog').should('not.exist');
  }
  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));
  getByTestId('ripa-form-container-continue').click();
  type('ripa-time-form-input', time);
  clickTestIds(['ripa-form-container-continue', 'ripa-response-to-call-yes', 'ripa-form-container-continue', 'ripa-duration-form-20']);
  // clearSnackBars();
  clickTestIds(['ripa-form-container-continue']);
  cy.get('[data-testid="ripa-action-taken-form-action-tab-school"]').click();
  cy.get('[data-testid="ripa-tabbed-location-form-school-autocomplete-input"]')
  .click();
  cy.get('*[data-option-index=2]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-type-of-assignment-officer-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-type-of-stop-select-dropdown"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-people-form-people-counter-number"]').contains('1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-gender-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-age-form-input-0"]').type('1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-race-form-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-disability-form-2024__sexual-orientation-select-0"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-primary-reason-form-box-0"]').click();

  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-traffic-violation-form-autocomplete-input"]').type('32 - ACCESSORY');
  cy.get('[data-testid="ripa-traffic-violation-form-box-0"]').click();
  getByTestId('ripa-form-container-continue').click({force: true});
  cy.get('[data-testid="ripa-reason-given-stopped-person-form-box-0"]').click();
  
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="pii-text-field"]').type('test 1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-action-taken-form-box-0-0"]')
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-action-taken-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-contraband-form-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-result-of-stop-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-result-of-stop-form-autocomplete-input-0-0"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-form-container-continue"]').click();
  cy.url().should('include', '/dashboard');
};
