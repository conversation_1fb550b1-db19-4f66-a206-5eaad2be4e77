declare namespace Cypress {
  interface Chainable {
    Graphql(
      query: string,
      variables?: unknown
      // TO DO
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ): Chainable<Cypress.Response<any>>;
    getDataIdCy({
      idAlias,
      options,
    }: {
      idAlias: string;
      options?: Partial<
        Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow
      >;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
    mockGeolocation(coords: { latitude: number; longitude: number; accuracy?: number }): Chainable<void>;
    decoupledDispatch: (action: string, payload?: unknown) => void;
  }
  interface RequestOptions extends Loggable, Timeoutable, Failable {
    auth: object;
    body: RequestBody;
    encoding: Encodings;
    followRedirect: boolean;
    form: boolean;
    gzip: boolean;
    headers: object;
    method: HttpMethod;
    qs: object;
    url: string;
  }
}
