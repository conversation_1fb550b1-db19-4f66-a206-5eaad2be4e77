export const getByTestId = (
  dataTestId: string,
  options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow & Cypress.Withinable>
): Cypress.Chainable<JQuery<HTMLElement>> => cy.get(`*[data-testid=${dataTestId}]`, options);

interface TypeOptions {
  validate?: boolean;
}
export const typeLogin = (testId: string, text: string, { validate = true }:
   TypeOptions = {}): Cypress.Chainable<JQuery<HTMLElement>> => {
  const el = getByTestId(testId);
  // Ensure element is ready before typing
  getByTestId(testId).should('be.visible').type(text);
  return validate ? el.should('have.value', text) : el;
};

export const type = (testId: string, text: string, { validate = true }:
  TypeOptions = {}): Cypress.Chainable<JQuery<HTMLElement>> => {
    const el = getByTestId(testId).type(text);
    return validate ? el.should('have.value', text) : el;
  };

interface ClickOptions {
  force?: boolean;
  multiple?: boolean;
}

export const clickTestIds = (testIds: string[], { force = true, multiple = true }: ClickOptions = {}): void =>
  testIds.forEach((testId: string) => getByTestId(testId).click({ force, multiple }));

export const clearSnackBars = (): void => {
  cy.get('.snackbar__box:not(.fading):not(.dead) .snackbar__box-close').click({
    multiple: true,
  });
  cy.get('.snackbar__box', { timeout: 10000 }).should('not.exist');
};
